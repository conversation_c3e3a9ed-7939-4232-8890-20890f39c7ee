# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an ESP32-S3 AI translator system that receives text via UART, translates it between Chinese and English using the MyMemory API, and displays results on an OLED screen. The system features:

- Real-time UART text reception with ring buffer management
- AI translation using MyMemory API with automatic language detection
- OLED display for showing translation results
- WiFi connectivity with automatic reconnection
- Complete error handling and retry mechanisms

## Common Commands

### Build and Flash Commands
```bash
# Set up ESP-IDF environment
source ~/esp/esp-idf/export.sh

# Build the project
idf.py build

# Flash to device
idf.py -p /dev/ttyUSB0 flash

# Monitor serial output
idf.py -p /dev/ttyUSB0 monitor

# Exit monitor
Ctrl+]
```

### Development Commands
```bash
# Clean build
idf.py fullclean

# Build and flash in one command
idf.py -p /dev/ttyUSB0 flash monitor

# Check configuration
idf.py menuconfig
```

## Architecture

### Core Components

1. **UART Reception System** (`main/main.c:505-530`)
   - Uses UART_NUM_1 for receiving text input
   - Event-driven architecture with FreeRTOS tasks
   - Ring buffer for efficient data handling

2. **Ring Buffer Management** (`main/main.c:78-330`)
   - Thread-safe circular buffers for input/output
   - Mutex protection for concurrent access
   - Line-based message processing

3. **Translation Engine** (`main/main.c:405-503`)
   - MyMemory API integration via HTTP client
   - Automatic language detection (Chinese/English)
   - JSON response parsing with cJSON

4. **Display System** (`main/main.c:68-76`)
   - LVGL-based UI with OLED support
   - Real-time status and translation display
   - SSD1306 controller support

5. **WiFi Management** (`main/main.c:743-781`)
   - Automatic connection with retry logic
   - Connection status monitoring
   - IP address display on OLED

### Hardware Configuration

- **OLED Display**: SSD1306 128x64 via I2C (GPIO5=SDA, GPIO4=SCL)
- **UART**: Internal UART1 for text input at 115200 baud
- **WiFi**: ESP32-S3 built-in WiFi for API connectivity
- **Button**: GPIO10 for menu navigation (interrupt-driven)

### Configuration Management

All system parameters are centralized in `main/config.h`:
- WiFi credentials
- Buffer sizes
- Task priorities
- API endpoints
- Hardware pin assignments

### Key Design Patterns

1. **Event-Driven Architecture**: UART events trigger processing tasks
2. **Producer-Consumer**: Ring buffers decouple input/output operations
3. **Retry Mechanisms**: Robust error handling for network operations
4. **Resource Management**: Proper cleanup of mutexes and buffers

## Important Notes

- Requires ESP-IDF v5.4 or higher
- Uses managed components from ESP Component Registry
- Chinese text detection uses basic UTF-8 byte checking
- Translation API calls include automatic retry with exponential backoff
- All network operations are protected by connection status checks

## Dependencies

The project uses these managed components:
- `espressif__esp_lvgl_port` - LVGL graphics library port
- `espressif__esp_lcd_sh1107` - SH1107 OLED controller driver
- `espressif__esp_codec_dev` - Audio codec device drivers
- `espressif__esp_websocket_client` - WebSocket client library
- `lvgl__lvgl` - LVGL graphics library

## Testing

To test the system:
1. Flash the firmware
2. Connect via serial at 115200 baud
3. Send text lines ending with `\n`
4. Monitor OLED display for translation results
5. Check serial output for system logs