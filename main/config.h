/*
 * ESP32-S3 AI翻译器配置文件
 * 统一管理所有系统配置参数
 */

#ifndef CONFIG_H
#define CONFIG_H

// ==================== 系统标识 ====================
#define TAG "TRANSLATOR"  // 日志标签

// ==================== WiFi配置 ====================
#define WIFI_SSID "203"           // WiFi网络名称
#define WIFI_PASSWORD "203203203" // WiFi密码

// ==================== 翻译API配置 ====================
#define TRANSLATION_API_URL "https://api.mymemory.translated.net/get"  // MyMemory免费翻译API
#define API_USER_AGENT "ESP32-Translator/1.0"  // API用户代理

// ==================== 缓冲区配置 ====================
#define INPUT_BUFFER_SIZE 1024    // 输入环形缓冲区大小(字节)
#define OUTPUT_BUFFER_SIZE 128    // 输出环形缓冲区大小(字节)
#define UART_BUFFER_SIZE 1024     // UART缓冲区大小(字节)

// ==================== 串口配置 ====================
#define UART_PORT_NUM UART_NUM_1  // 使用的UART端口号
#define UART_BAUD_RATE 115200     // 串口波特率
#define UART_DATA_BITS_NUM UART_DATA_8_BITS    // 数据位数
#define UART_PARITY_MODE UART_PARITY_DISABLE   // 校验位
#define UART_STOP_BITS_NUM UART_STOP_BITS_1    // 停止位
#define UART_FLOW_CTRL_MODE UART_HW_FLOWCTRL_DISABLE  // 流控制

// ==================== I2C配置 ====================
#define I2C_BUS_PORT 0            // I2C总线端口

// ==================== LCD显示配置 ====================
#define LCD_PIXEL_CLOCK_HZ (400 * 1000)  // LCD像素时钟频率
#define LCD_PIN_SDA 5             // SDA引脚
#define LCD_PIN_SCL 4             // SCL引脚
#define LCD_PIN_RST -1            // 复位引脚(-1表示不使用)
#define LCD_I2C_ADDR 0x3C         // LCD I2C地址
#define LCD_WIDTH 128             // LCD宽度(像素)
#define LCD_CMD_BITS 8            // LCD命令位数
#define LCD_PARAM_BITS 8          // LCD参数位数

// ==================== 任务配置 ====================
#define UART_TASK_STACK_SIZE 3072     // UART任务栈大小 - 优化内存使用
#define PROCESS_TASK_STACK_SIZE 6144  // 处理任务栈大小 - 优化内存使用
#define UART_TASK_PRIORITY 6          // UART任务优先级 - 提高实时性
#define PROCESS_TASK_PRIORITY 5       // 处理任务优先级 - 提高实时性
#define UART_QUEUE_SIZE 16            // UART事件队列大小 - 优化内存

// ==================== 性能配置 ====================
#define PROCESS_TASK_DELAY_MS 10      // 处理任务延迟(毫秒) - 优化响应速度
#define HTTP_TIMEOUT_MS 10000         // HTTP请求超时时间(毫秒)
#define MAX_RETRY_COUNT 3             // 最大重试次数
#define MUTEX_TIMEOUT_MS 1000         // 互斥锁超时时间(毫秒)
#define WIFI_RETRY_DELAY_MS 5000      // WiFi重连延迟(毫秒)
#define TRANSLATION_RETRY_DELAY_MS 2000  // 翻译重试延迟(毫秒)

// ==================== 内存配置 ====================
#define MAX_LINE_LENGTH 512           // 最大行长度
#define MAX_POST_DATA_SIZE 512        // 最大POST数据大小
#define IP_STRING_SIZE 16             // IP字符串大小
#define DISPLAY_STRING_SIZE 128       // 显示字符串大小

// ==================== AI翻译配置 ====================
#define AI_PROVIDER_VOLCENGINE 1          // 火山引擎提供商
#define AI_PROVIDER_MYMEMORY 2            // MyMemory提供商（备用）
#define AI_TRANSLATION_PROVIDER AI_PROVIDER_VOLCENGINE  // 默认使用火山引擎
#define AI_API_KEY "your_volcengine_api_key"            // API密钥（需要用户配置）
#define AI_MODEL_ID "doubao-lite-4k"                    // 模型ID
#define AI_BASE_URL "https://ark.cn-beijing.volces.com/api/v3/"  // 基础URL
#define AI_SYSTEM_PROMPT "你是一个专业的翻译助手，请将用户输入的文本在中文和英文之间进行准确翻译。只返回翻译结果，不要添加任何解释。"  // 系统提示词
#define AI_MAX_RESPONSE_SIZE 2048         // 最大响应大小(字节)
#define AI_REQUEST_TIMEOUT_MS 15000       // AI请求超时时间(毫秒)

#endif // CONFIG_H
