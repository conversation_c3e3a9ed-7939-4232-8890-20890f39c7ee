/*
 * ESP32-S3 AI翻译器 - AI翻译模块头文件
 * 定义AI翻译模块的核心接口、数据结构和枚举类型
 */

#ifndef AI_TRANSLATOR_H
#define AI_TRANSLATOR_H

#include "esp_err.h"
#include "esp_log.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 枚举定义 ====================

/**
 * AI服务提供商枚举
 */
typedef enum {
    AI_PROVIDER_VOLCENGINE = 1,  // 火山引擎提供商
    AI_PROVIDER_MYMEMORY = 2     // MyMemory提供商（备用）
} ai_provider_t;

// ==================== 结构体定义 ====================

/**
 * AI翻译器配置结构体
 */
typedef struct {
    char *api_key;           // API密钥
    char *model_id;          // 模型ID
    char *base_url;          // 基础URL
    ai_provider_t provider;  // 服务提供商
    int timeout_ms;          // 超时时间(毫秒)
    int max_retries;         // 最大重试次数
} ai_translator_config_t;

// ==================== 回调函数类型定义 ====================

/**
 * AI翻译结果回调函数类型
 * @param result 翻译结果字符串，成功时非NULL
 * @param error 错误码，ESP_OK表示成功
 */
typedef void (*ai_translate_callback_t)(const char *result, esp_err_t error);

// ==================== 核心API函数声明 ====================

/**
 * 初始化AI翻译器
 * @param config 翻译器配置参数
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t ai_translator_init(const ai_translator_config_t *config);

/**
 * 异步执行翻译
 * @param text 待翻译的文本
 * @param callback 翻译完成后的回调函数
 * @return ESP_OK 成功提交翻译请求，其他值表示错误
 */
esp_err_t ai_translator_translate_async(const char *text, ai_translate_callback_t callback);

/**
 * 设置AI服务提供商
 * @param provider 服务提供商类型
 * @return ESP_OK 成功，其他值表示错误
 */
esp_err_t ai_translator_set_provider(ai_provider_t provider);

/**
 * 清理AI翻译器资源
 */
void ai_translator_cleanup(void);

// ==================== 辅助函数声明 ====================

/**
 * 获取当前配置的服务提供商
 * @return 当前的服务提供商类型
 */
ai_provider_t ai_translator_get_provider(void);

/**
 * 检查AI翻译器是否已初始化
 * @return true 已初始化，false 未初始化
 */
bool ai_translator_is_initialized(void);

#ifdef __cplusplus
}
#endif

#endif // AI_TRANSLATOR_H
