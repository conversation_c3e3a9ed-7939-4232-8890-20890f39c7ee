/*
 * ESP32-S3 AI翻译器 - AI翻译模块实现
 * 实现火山引擎AI翻译功能，支持OpenAI兼容API
 */

#include "ai_translator.h"
#include "config.h"
#include "esp_http_client.h"
#include "cJSON.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>
#include <stdlib.h>

// ==================== 内部状态管理 ====================

static const char *AI_TAG = "AI_TRANSLATOR";  // 日志标签
static ai_translator_config_t g_ai_config;    // 全局配置
static bool g_ai_initialized = false;         // 初始化状态
static ai_translate_callback_t g_current_callback = NULL;  // 当前回调函数

// ==================== 内部函数声明 ====================

static bool ai_is_chinese_text(const char *text);
static esp_err_t ai_http_event_handler(esp_http_client_event_t *evt);
static char* ai_build_json_request(const char *text, const char *from_lang, const char *to_lang);

// ==================== 语言检测函数 ====================

/**
 * 检测文本是否为中文（复用现有逻辑）
 */
static bool ai_is_chinese_text(const char *text) {
    if (text == NULL) return false;
    
    for (int i = 0; text[i] != '\0'; i++) {
        unsigned char c = (unsigned char)text[i];
        // Check for UTF-8 Chinese characters (basic range)
        if (c >= 0xE4 && c <= 0xE9) {
            return true;
        }
    }
    return false;
}

// ==================== HTTP事件处理器 ====================

/**
 * AI翻译HTTP事件处理器
 */
static esp_err_t ai_http_event_handler(esp_http_client_event_t *evt) {
    static char response_buffer[AI_MAX_RESPONSE_SIZE];  // 使用配置的响应大小
    static int response_len = 0;

    switch (evt->event_id) {
        case HTTP_EVENT_ON_DATA:
            // 累积响应数据
            if (evt->data_len > 0 && response_len + evt->data_len < sizeof(response_buffer) - 1) {
                memcpy(response_buffer + response_len, evt->data, evt->data_len);
                response_len += evt->data_len;
                response_buffer[response_len] = '\0';
            }
            break;

        case HTTP_EVENT_ON_FINISH:
            // 处理完整响应
            if (response_len > 0) {
                ESP_LOGI(AI_TAG, "Received AI response (%d bytes)", response_len);

                // 解析OpenAI格式的JSON响应
                cJSON *root = cJSON_Parse(response_buffer);
                if (root) {
                    cJSON *choices = cJSON_GetObjectItem(root, "choices");
                    if (choices && cJSON_IsArray(choices) && cJSON_GetArraySize(choices) > 0) {
                        cJSON *first_choice = cJSON_GetArrayItem(choices, 0);
                        if (first_choice) {
                            cJSON *message = cJSON_GetObjectItem(first_choice, "message");
                            if (message) {
                                cJSON *content = cJSON_GetObjectItem(message, "content");
                                if (content && cJSON_IsString(content)) {
                                    const char *translation = content->valuestring;
                                    ESP_LOGI(AI_TAG, "AI Translation result: %s", translation);
                                    
                                    // 调用用户回调函数
                                    if (g_current_callback) {
                                        g_current_callback(translation, ESP_OK);
                                    }
                                } else {
                                    ESP_LOGW(AI_TAG, "No content found in AI response");
                                    if (g_current_callback) {
                                        g_current_callback(NULL, ESP_ERR_INVALID_RESPONSE);
                                    }
                                }
                            } else {
                                ESP_LOGW(AI_TAG, "No message found in AI response");
                                if (g_current_callback) {
                                    g_current_callback(NULL, ESP_ERR_INVALID_RESPONSE);
                                }
                            }
                        }
                    } else {
                        ESP_LOGW(AI_TAG, "No choices found in AI response");
                        if (g_current_callback) {
                            g_current_callback(NULL, ESP_ERR_INVALID_RESPONSE);
                        }
                    }
                    cJSON_Delete(root);
                } else {
                    ESP_LOGE(AI_TAG, "Failed to parse AI JSON response: %s", response_buffer);
                    if (g_current_callback) {
                        g_current_callback(NULL, ESP_ERR_INVALID_RESPONSE);
                    }
                }
            } else {
                ESP_LOGW(AI_TAG, "Empty AI response received");
                if (g_current_callback) {
                    g_current_callback(NULL, ESP_ERR_INVALID_RESPONSE);
                }
            }

            // 重置缓冲区
            response_len = 0;
            g_current_callback = NULL;  // 清除回调函数
            break;

        case HTTP_EVENT_ERROR:
            ESP_LOGE(AI_TAG, "AI HTTP event error");
            response_len = 0;
            if (g_current_callback) {
                g_current_callback(NULL, ESP_ERR_HTTP_BASE);
                g_current_callback = NULL;
            }
            break;

        default:
            break;
    }
    return ESP_OK;
}

// ==================== JSON请求构造 ====================

/**
 * 构造OpenAI格式的JSON请求体
 */
static char* ai_build_json_request(const char *text, const char *from_lang, const char *to_lang) {
    cJSON *root = cJSON_CreateObject();
    cJSON *model = cJSON_CreateString(g_ai_config.model_id);
    cJSON *messages = cJSON_CreateArray();
    
    // 系统消息
    cJSON *system_msg = cJSON_CreateObject();
    cJSON_AddStringToObject(system_msg, "role", "system");
    cJSON_AddStringToObject(system_msg, "content", AI_SYSTEM_PROMPT);
    cJSON_AddItemToArray(messages, system_msg);
    
    // 用户消息
    cJSON *user_msg = cJSON_CreateObject();
    cJSON_AddStringToObject(user_msg, "role", "user");
    
    // 构造翻译指令
    char user_content[512];
    snprintf(user_content, sizeof(user_content), 
             "请将以下文本从%s翻译为%s：\n%s", 
             from_lang, to_lang, text);
    cJSON_AddStringToObject(user_msg, "content", user_content);
    cJSON_AddItemToArray(messages, user_msg);
    
    // 添加到根对象
    cJSON_AddItemToObject(root, "model", model);
    cJSON_AddItemToObject(root, "messages", messages);
    cJSON_AddNumberToObject(root, "max_tokens", 1000);
    cJSON_AddNumberToObject(root, "temperature", 0.3);
    
    char *json_string = cJSON_Print(root);
    cJSON_Delete(root);
    
    return json_string;
}

// ==================== 核心API实现 ====================

/**
 * 初始化AI翻译器
 */
esp_err_t ai_translator_init(const ai_translator_config_t *config) {
    if (config == NULL) {
        ESP_LOGE(AI_TAG, "AI translator config is NULL");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (config->api_key == NULL || config->model_id == NULL || config->base_url == NULL) {
        ESP_LOGE(AI_TAG, "AI translator config parameters are NULL");
        return ESP_ERR_INVALID_ARG;
    }
    
    // 复制配置
    g_ai_config = *config;
    g_ai_initialized = true;
    g_current_callback = NULL;
    
    ESP_LOGI(AI_TAG, "AI translator initialized successfully");
    ESP_LOGI(AI_TAG, "Provider: %d, Model: %s", config->provider, config->model_id);
    
    return ESP_OK;
}

/**
 * 异步执行翻译
 */
esp_err_t ai_translator_translate_async(const char *text, ai_translate_callback_t callback) {
    if (!g_ai_initialized) {
        ESP_LOGE(AI_TAG, "AI translator not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (text == NULL || callback == NULL) {
        ESP_LOGE(AI_TAG, "Invalid parameters for AI translation");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (strlen(text) == 0) {
        ESP_LOGW(AI_TAG, "Empty text for AI translation");
        callback(NULL, ESP_ERR_INVALID_ARG);
        return ESP_ERR_INVALID_ARG;
    }
    
    // 检测语言并设置翻译方向
    bool is_chinese = ai_is_chinese_text(text);
    const char *from_lang = is_chinese ? "中文" : "英文";
    const char *to_lang = is_chinese ? "英文" : "中文";
    
    ESP_LOGI(AI_TAG, "AI Translating (%s->%s): %s", from_lang, to_lang, text);
    
    // 构造JSON请求
    char *json_data = ai_build_json_request(text, from_lang, to_lang);
    if (json_data == NULL) {
        ESP_LOGE(AI_TAG, "Failed to build AI JSON request");
        callback(NULL, ESP_ERR_NO_MEM);
        return ESP_ERR_NO_MEM;
    }
    
    // 设置当前回调函数
    g_current_callback = callback;
    
    // 重试机制（复用现有模式）
    for (int retry = 0; retry < g_ai_config.max_retries; retry++) {
        if (retry > 0) {
            ESP_LOGI(AI_TAG, "AI translation retry attempt %d/%d", retry + 1, g_ai_config.max_retries);
            vTaskDelay(pdMS_TO_TICKS(TRANSLATION_RETRY_DELAY_MS));
        }
        
        // 配置HTTP客户端
        esp_http_client_config_t config = {
            .url = g_ai_config.base_url,
            .event_handler = ai_http_event_handler,
            .method = HTTP_METHOD_POST,
            .timeout_ms = g_ai_config.timeout_ms,
        };
        
        esp_http_client_handle_t client = esp_http_client_init(&config);
        if (client == NULL) {
            ESP_LOGE(AI_TAG, "Failed to initialize AI HTTP client (retry %d)", retry + 1);
            continue;
        }
        
        // 设置请求头
        esp_http_client_set_header(client, "Content-Type", "application/json");
        
        // 设置Authorization头
        char auth_header[256];
        snprintf(auth_header, sizeof(auth_header), "Bearer %s", g_ai_config.api_key);
        esp_http_client_set_header(client, "Authorization", auth_header);
        
        // 设置POST数据
        esp_http_client_set_post_field(client, json_data, strlen(json_data));
        
        esp_err_t err = esp_http_client_perform(client);
        int status_code = esp_http_client_get_status_code(client);
        
        if (err == ESP_OK && status_code == 200) {
            ESP_LOGI(AI_TAG, "AI translation successful (retry %d)", retry + 1);
            esp_http_client_cleanup(client);
            free(json_data);
            return ESP_OK;  // 成功，退出重试循环
        } else {
            ESP_LOGW(AI_TAG, "AI translation failed - Error: %s, Status: %d (retry %d)",
                    esp_err_to_name(err), status_code, retry + 1);
        }
        
        esp_http_client_cleanup(client);
    }
    
    ESP_LOGE(AI_TAG, "AI translation failed after %d retries", g_ai_config.max_retries);
    free(json_data);
    
    // 通知回调函数失败
    if (g_current_callback) {
        g_current_callback(NULL, ESP_FAIL);
        g_current_callback = NULL;
    }
    
    return ESP_FAIL;
}

/**
 * 设置AI服务提供商
 */
esp_err_t ai_translator_set_provider(ai_provider_t provider) {
    if (!g_ai_initialized) {
        ESP_LOGE(AI_TAG, "AI translator not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (provider != AI_PROVIDER_VOLCENGINE && provider != AI_PROVIDER_MYMEMORY) {
        ESP_LOGE(AI_TAG, "Invalid AI provider: %d", provider);
        return ESP_ERR_INVALID_ARG;
    }

    g_ai_config.provider = provider;
    ESP_LOGI(AI_TAG, "AI provider set to: %d", provider);

    return ESP_OK;
}

/**
 * 清理AI翻译器资源
 */
void ai_translator_cleanup(void) {
    if (g_ai_initialized) {
        g_ai_initialized = false;
        g_current_callback = NULL;
        memset(&g_ai_config, 0, sizeof(g_ai_config));
        ESP_LOGI(AI_TAG, "AI translator cleaned up");
    }
}

/**
 * 获取当前配置的服务提供商
 */
ai_provider_t ai_translator_get_provider(void) {
    if (!g_ai_initialized) {
        return AI_PROVIDER_MYMEMORY;  // 默认返回备用提供商
    }
    return g_ai_config.provider;
}

/**
 * 检查AI翻译器是否已初始化
 */
bool ai_translator_is_initialized(void) {
    return g_ai_initialized;
}
