[0/1] Re-running CMake...
-- git rev-parse returned 'fatal: not a git repository (or any parent up to mount point /)
Stopping at filesystem boundary (GIT_DISCOVERY_ACROSS_FILESYSTEM not set).'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32s3
NOTICE: Processing 6 dependencies:
NOTICE: [1/6] espressif/esp_codec_dev (1.3.5)
NOTICE: [2/6] espressif/esp_lcd_sh1107 (1.1.0)
NOTICE: [3/6] espressif/esp_lvgl_port (1.4.0)
NOTICE: [4/6] espressif/esp_websocket_client (1.4.0)
NOTICE: [5/6] lvgl/lvgl (8.4.0)
NOTICE: [6/6] idf (5.4.1)
-- Project sdkconfig file /home/<USER>/ESP32/ai_led/sdkconfig
Loading defaults file /home/<USER>/ESP32/ai_led/sdkconfig.defaults...
-- Compiler supported targets: xtensa-esp-elf
-- App "ai_led" version: 1
-- Adding linker script /home/<USER>/ESP32/ai_led/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /home/<USER>/ESP32/ai_led/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__esp_codec_dev espressif__esp_lcd_sh1107 espressif__esp_lvgl_port espressif__esp_websocket_client esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /home/<USER>/esp/v5.4.1/esp-idf/components/app_trace /home/<USER>/esp/v5.4.1/esp-idf/components/app_update /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support /home/<USER>/esp/v5.4.1/esp-idf/components/bt /home/<USER>/esp/v5.4.1/esp-idf/components/cmock /home/<USER>/esp/v5.4.1/esp-idf/components/console /home/<USER>/esp/v5.4.1/esp-idf/components/cxx /home/<USER>/esp/v5.4.1/esp-idf/components/driver /home/<USER>/esp/v5.4.1/esp-idf/components/efuse /home/<USER>/esp/v5.4.1/esp-idf/components/esp-tls /home/<USER>/esp/v5.4.1/esp-idf/components/esp_adc /home/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format /home/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format /home/<USER>/esp/v5.4.1/esp-idf/components/esp_coex /home/<USER>/esp/v5.4.1/esp-idf/components/esp_common /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart /home/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag /home/<USER>/esp/v5.4.1/esp-idf/components/esp_eth /home/<USER>/esp/v5.4.1/esp-idf/components/esp_event /home/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub /home/<USER>/esp/v5.4.1/esp-idf/components/esp_hid /home/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client /home/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server /home/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota /home/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server /home/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support /home/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd /home/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl /home/<USER>/esp/v5.4.1/esp-idf/components/esp_mm /home/<USER>/esp/v5.4.1/esp-idf/components/esp_netif /home/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack /home/<USER>/esp/v5.4.1/esp-idf/components/esp_partition /home/<USER>/esp/v5.4.1/esp-idf/components/esp_phy /home/<USER>/esp/v5.4.1/esp-idf/components/esp_pm /home/<USER>/esp/v5.4.1/esp-idf/components/esp_psram /home/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom /home/<USER>/esp/v5.4.1/esp-idf/components/esp_security /home/<USER>/esp/v5.4.1/esp-idf/components/esp_system /home/<USER>/esp/v5.4.1/esp-idf/components/esp_timer /home/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console /home/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi /home/<USER>/esp/v5.4.1/esp-idf/components/espcoredump /home/<USER>/ESP32/ai_led/managed_components/espressif__esp_codec_dev /home/<USER>/ESP32/ai_led/managed_components/espressif__esp_lcd_sh1107 /home/<USER>/ESP32/ai_led/managed_components/espressif__esp_lvgl_port /home/<USER>/ESP32/ai_led/managed_components/espressif__esp_websocket_client /home/<USER>/esp/v5.4.1/esp-idf/components/esptool_py /home/<USER>/esp/v5.4.1/esp-idf/components/fatfs /home/<USER>/esp/v5.4.1/esp-idf/components/freertos /home/<USER>/esp/v5.4.1/esp-idf/components/hal /home/<USER>/esp/v5.4.1/esp-idf/components/heap /home/<USER>/esp/v5.4.1/esp-idf/components/http_parser /home/<USER>/esp/v5.4.1/esp-idf/components/idf_test /home/<USER>/esp/v5.4.1/esp-idf/components/ieee802154 /home/<USER>/esp/v5.4.1/esp-idf/components/json /home/<USER>/esp/v5.4.1/esp-idf/components/log /home/<USER>/ESP32/ai_led/managed_components/lvgl__lvgl /home/<USER>/esp/v5.4.1/esp-idf/components/lwip /home/<USER>/ESP32/ai_led/main /home/<USER>/esp/v5.4.1/esp-idf/components/mbedtls /home/<USER>/esp/v5.4.1/esp-idf/components/mqtt /home/<USER>/esp/v5.4.1/esp-idf/components/newlib /home/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash /home/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider /home/<USER>/esp/v5.4.1/esp-idf/components/openthread /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table /home/<USER>/esp/v5.4.1/esp-idf/components/perfmon /home/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c /home/<USER>/esp/v5.4.1/esp-idf/components/protocomm /home/<USER>/esp/v5.4.1/esp-idf/components/pthread /home/<USER>/esp/v5.4.1/esp-idf/components/rt /home/<USER>/esp/v5.4.1/esp-idf/components/sdmmc /home/<USER>/esp/v5.4.1/esp-idf/components/soc /home/<USER>/esp/v5.4.1/esp-idf/components/spi_flash /home/<USER>/esp/v5.4.1/esp-idf/components/spiffs /home/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport /home/<USER>/esp/v5.4.1/esp-idf/components/touch_element /home/<USER>/esp/v5.4.1/esp-idf/components/ulp /home/<USER>/esp/v5.4.1/esp-idf/components/unity /home/<USER>/esp/v5.4.1/esp-idf/components/usb /home/<USER>/esp/v5.4.1/esp-idf/components/vfs /home/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling /home/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning /home/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant /home/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (6.0s)
-- Generating done (0.5s)
-- Build files have been written to: /home/<USER>/ESP32/ai_led/build
[1/8] Performing build step for 'bootloader'
[0/1] Re-running CMake...
-- Building ESP-IDF components for target esp32s3
-- Project sdkconfig file /home/<USER>/ESP32/ai_led/sdkconfig
-- Compiler supported targets: xtensa-esp-elf
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- Bootloader project name: "bootloader" version: 1
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.ld
-- Adding linker script /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/main/ld/esp32s3/bootloader.rom.ld
-- Components: bootloader bootloader_support efuse esp_app_format esp_bootloader_format esp_common esp_hw_support esp_rom esp_security esp_system esptool_py freertos hal log main micro-ecc newlib partition_table soc spi_flash xtensa
-- Component paths: /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support /home/<USER>/esp/v5.4.1/esp-idf/components/efuse /home/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format /home/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format /home/<USER>/esp/v5.4.1/esp-idf/components/esp_common /home/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support /home/<USER>/esp/v5.4.1/esp-idf/components/esp_rom /home/<USER>/esp/v5.4.1/esp-idf/components/esp_security /home/<USER>/esp/v5.4.1/esp-idf/components/esp_system /home/<USER>/esp/v5.4.1/esp-idf/components/esptool_py /home/<USER>/esp/v5.4.1/esp-idf/components/freertos /home/<USER>/esp/v5.4.1/esp-idf/components/hal /home/<USER>/esp/v5.4.1/esp-idf/components/log /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/main /home/<USER>/esp/v5.4.1/esp-idf/components/bootloader/subproject/components/micro-ecc /home/<USER>/esp/v5.4.1/esp-idf/components/newlib /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table /home/<USER>/esp/v5.4.1/esp-idf/components/soc /home/<USER>/esp/v5.4.1/esp-idf/components/spi_flash /home/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (2.0s)
-- Generating done (0.0s)
-- Build files have been written to: /home/<USER>/ESP32/ai_led/build/bootloader
[1/2] cd /home/<USER>/ESP32/ai_led/build/bootloader/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.13_env/bin/python /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /home/<USER>/ESP32/ai_led/build/bootloader/bootloader.bin
Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.
[2/8] No install step for 'bootloader'
[3/8] Completed 'bootloader'
[4/8] Generating ld/sections.ld
[5/8] Linking CXX executable ai_led.elf
[6/8] Generating binary image from built executable
esptool.py v4.9.0
Creating esp32s3 image...
Merged 2 ELF sections
Successfully created esp32s3 image.
Generated /home/<USER>/ESP32/ai_led/build/ai_led.bin
[7/8] cd /home/<USER>/ESP32/ai_led/build/esp-idf/esptool_py && /home/<USER>/.espressif/python_env/idf5.4_py3.13_env/bin/python /home/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /home/<USER>/ESP32/ai_led/build/partition_table/partition-table.bin /home/<USER>/ESP32/ai_led/build/ai_led.bin
ai_led.bin binary size 0xfd070 bytes. Smallest app partition is 0x659000 bytes. 0x55bf90 bytes (84%) free.
